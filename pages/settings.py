# coding=utf-8
from functools import cmp_to_key

import pandas as pd
from PyQt5.QtWidgets import *
from qfluentwidgets import *
from qfluentwidgets.components.date_time.picker_base import SeparatorWidget

from menu import days,generate_time,display_df_in_table
from style import *
from wr_settings import *

class Settings(QFrame):
    def update_table_preview(self):
        save_settings(self)
        # 根据是否显示教师姓名调整表格信息和样式
        if self.cfg.show_teachers.value:
            info="课程信息\n(教师姓名)"
        else:
            info="课程信息"

        # 生成表格预览样式
        table_style=[]
        for day in range(5):
            table_style.append({"星期":days[day+1]})
            for i in range(1,self.cfg.morning_class_num.value+1):
                table_style[day][f"上午第{i}节"]=info
            for i in range(1,self.cfg.afternoon_class_num.value+1):
                table_style[day][f"下午第{i}节"]=info
        self.table=pd.DataFrame(table_style)
        # 根据设置选择表格显示方式
        display_df_in_table(self.table_style_show,self.table.transpose())
        self.table_style_show.setFixedHeight(50*self.table_style_show.rowCount()+2)

    def pick_lessons_info(self):
        user_info_file,_=QFileDialog.getOpenFileName(
            self,
            "选择课程信息文件",
            "",
            "Excel文件 (*.xlsx *.xls)"
        )
        # 处理上传的课程信息文件或使用已有配置
        if user_info_file:
            try:
                user_info=pd.read_excel(user_info_file)
                rename_dict={}
                for i in range(2,len(user_info.keys()),2):
                    rename_dict[user_info.keys()[i]]=user_info.keys()[i-1]+" - 任课老师"
                    rename_dict[user_info.keys()[i-1]]=user_info.keys()[i-1]+" - 课时"
                user_info=user_info.rename(columns=rename_dict)
                self.cfg.lessons_info.value=user_info.to_json(orient="records", lines=False, force_ascii=False)

                new_subjects=[user_info.keys()[i][:-5] for i in range(1,len(user_info.keys()),2)]
                subjects_table_keys=["班级"]
                for subject in new_subjects:
                    subjects_table_keys.append(subject+" - 课时")
                    subjects_table_keys.append(subject+" - 任课老师")
                self.cfg.subjects_info.value=json.dumps(new_subjects)
                save_settings(self)
            except Exception as e:
                save_settings(self,False,str(e))
        else:
            if self.cfg.lessons_info.value!="":
                self.cfg.lessons_info.value=pd.DataFrame(json.loads(self.cfg.lessons_info.value)).to_json(orient="records", lines=False, force_ascii=False)

    def add_rules(self):
        rule=self.rule_combobox.currentData()
        rule["type"]=rule["type"].split("|")


    def set_time(self):
        pass

    def __init__(self,parent=None):

        super().__init__(parent=parent)
        self.setObjectName("Settings")
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20,20,0,0)

        # === 创建可滚动区域 ===
        scroll_area=SingleDirectionScrollArea(orient=Qt.Vertical)
        scroll_area.setStyleSheet("QScrollArea{background: transparent; border: none}")
        scroll_area.setWidgetResizable(True)

        # === 创建内容容器 ===
        view=QWidget()
        view.setStyleSheet("QWidget{background: transparent}")
        layout = QVBoxLayout(view)

        self.title = title("设置",self,layout)
        # 读取配置文件
        self.cfg=load_settings()

        # 表格样式设置区域
        biggersubheader("表格样式",self,layout)

        # 设置每天上午和下午的课程数量
        morning_class_num=RangeSettingCard(self.cfg.morning_class_num,FluentIcon.FLAG,title="每天上午上课数量",content="学校每天上午的上课数量")
        morning_class_num.valueChanged.connect(self.update_table_preview)
        add_widget(morning_class_num,layout,0)
        afternoon_class_num=RangeSettingCard(self.cfg.afternoon_class_num,FluentIcon.FLAG,title="每天上午上课数量",content="学校每天下午的上课数量")
        afternoon_class_num.valueChanged.connect(self.update_table_preview)
        add_widget(afternoon_class_num,layout,0)

        # 设置是否显示教师姓名和表格排版方式
        show_teachers=SwitchSettingCard(configItem=self.cfg.show_teachers,icon=FluentIcon.TAG,title="显示教师姓名",content="在课程名称下方标注任课教师姓名")
        show_teachers.checkedChanged.connect(self.update_table_preview)
        add_widget(show_teachers,layout,0)

        # 根据是否显示教师姓名调整表格信息和样式
        if self.cfg.show_teachers.value:
            info="课程信息\n(教师姓名)"
        else:
            info="课程信息"

        # 生成表格预览样式
        table_style=[]
        for day in range(5):
            table_style.append({"星期":days[day+1]})
            for i in range(1,self.cfg.morning_class_num.value+1):
                table_style[day][f"上午第{i}节"]=info
            for i in range(1,self.cfg.afternoon_class_num.value+1):
                table_style[day][f"下午第{i}节"]=info

        # 预览课程表
        subheader("预览",self,layout)
        write("一年级1班课程表",self,layout,0)
        self.table=pd.DataFrame(table_style)

        self.table_style_show=TableWidget()
        self.table_style_show.setBorderVisible(True)
        self.table_style_show.setFont(fonts.subheader)
        self.table_style_show.setBorderRadius(8)
        self.table_style_show.verticalHeader().setDefaultSectionSize(50)
        self.table_style_show.horizontalHeader().setDefaultSectionSize(155)
        self.table_style_show.horizontalHeader().setVisible(False)
        self.table_style_show.setEditTriggers(TableWidget.NoEditTriggers)
        # 根据设置选择表格显示方式
        display_df_in_table(self.table_style_show,self.table.transpose())
        self.table_style_show.setFixedHeight(50*self.table_style_show.rowCount()+2)
        add_widget(self.table_style_show,layout)

        add_widget(SeparatorWidget(orient=Qt.Horizontal),layout)

        # 课程信息设置区域
        biggersubheader("课程信息",self,layout)

        # 上传课程信息文件
        self.user_info_file=PushSettingCard(text="选择文件",icon=FluentIcon.INFO,title="课程信息文件",content="存储任课老师及课时、班级信息的表格")
        add_widget(self.user_info_file,layout)
        self.user_info_file.clicked.connect(self.pick_lessons_info)
        lessons_info=pd.DataFrame(json.loads(self.cfg.lessons_info.value))
        lessons_info_table=TableWidget()
        display_df_in_table(lessons_info_table,lessons_info)
        lessons_info_table.setFixedHeight(500)
        lessons_info_table.setEditTriggers(TableWidget.NoEditTriggers)
        add_widget(lessons_info_table,layout)

        # 保存学科信息
        new_subjects=[lessons_info.keys()[i][:-5] for i in range(1,len(lessons_info.keys()),2)]
        subjects_table_keys=["班级"]
        for subject in new_subjects:
            subjects_table_keys.append(subject+" - 课时")
            subjects_table_keys.append(subject+" - 任课老师")
        self.cfg.subjects_info.value=new_subjects

        add_widget(SeparatorWidget(orient=Qt.Horizontal),layout)

        # 生成规则设置区域
        biggersubheader("生成规则",self,layout)
        rule_data=[{"type":"{Subject}|学科必须排在|{Time}","func":self.set_time}]

        # 筛选非0.5课时的学科
        # 所有学科
        subjects=[]
        for subject in self.cfg.subjects_info.value:
            subjects.append(subject)

        self.rule_combo=ComboBox()
        for rule in rule_data:
            self.rule_combo.addItem(rule["type"].replace("|",""),userData=rule)
        self.rule_combo.setFixedWidth(400)
        self.rule_combo.currentIndexChanged.connect(self.add_rules)
        add_widget(self.rule_combo,layout)

        self.rules_list=ListWidget()
        for rule in self.cfg.rules.value:
            self.rules_list.addItem(rule)
        add_widget(self.rules_list,layout)

        # === 设置滚动区域内容 ===
        scroll_area.setWidget(view)
        main_layout.addWidget(scroll_area)
