# 测试settings.py的规则设置功能
import sys
import json
from PyQt5.QtWidgets import QApplication
from pages.settings import Settings
from wr_settings import load_settings

def test_settings():
    app = QApplication(sys.argv)
    
    # 创建设置页面
    settings_page = Settings()
    
    # 测试配置加载
    cfg = load_settings()
    print("配置加载成功")
    
    # 测试规则功能
    print("测试规则相关方法...")
    
    # 模拟添加规则
    settings_page.current_rule_parts = ["{Subject}", "学科必须排在", "{Time}"]
    settings_page.rule_params = {}
    
    print("规则设置功能测试完成")
    
    app.quit()

if __name__ == "__main__":
    test_settings()
